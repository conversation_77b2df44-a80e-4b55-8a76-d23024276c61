appid = 'AK20250509HUPZKK'
appkey = '35a763bb9c7f10759dceb399b903dc28'

import hashlib
import hmac
import http
import requests
import json
from typing import Dict, Optional

ACCESS_KEY = 'AK20250509HUPZKK'
SECRET_KEY = '35a763bb9c7f10759dceb399b903dc28'


def _get_kso1_signature(method, uri, content_type, kso_date, request_body):
    sha256_hex = ''
    if request_body is not None and len(request_body) > 0:
        sha256_obj = hashlib.sha256()
        sha256_obj.update(request_body.encode())
        sha256_hex = sha256_obj.hexdigest()

    mac = hmac.new(bytes(SECRET_KEY, 'utf-8'),
                   bytes('KSO-1' + method + uri + content_type + kso_date + sha256_hex, 'utf-8'),
                   hashlib.sha256)
    return mac.hexdigest()


def kso1_sign(method, uri, content_type, kso_date, request_body):
    kso_signature = _get_kso1_signature(method, uri, content_type, kso_date, request_body)
    authorization = 'KSO-1 {}:{}'.format(ACCESS_KEY, kso_signature)
    return {
        'X-Kso-Date': kso_date,
        'X-Kso-Authorization': authorization
    }


def get_access_token(code: str, app_id: Optional[str] = None, app_key: Optional[str] = None) -> Dict:
    """
    获取用户访问令牌

    Args:
        code: 用户授权返回的授权码
        app_id: 应用 APPID，默认使用全局 ACCESS_KEY
        app_key: 应用 APPKEY，默认使用全局 SECRET_KEY

    Returns:
        包含 access_token、refresh_token 等信息的字典

    Raises:
        requests.RequestException: 网络请求异常
        ValueError: API 返回错误
    """
    # 使用传入参数或默认值
    app_id = app_id or ACCESS_KEY
    app_key = app_key or SECRET_KEY

    # 构建请求参数
    params = {
        'code': code,
        'app_id': app_id,
        'app_key': app_key
    }

    # 发送请求
    url = 'https://developer.kdocs.cn/api/v1/oauth2/access_token'
    headers = {'Content-Type': 'application/json'}

    try:
        response = requests.get(url, params=params, headers=headers, timeout=30)
        response.raise_for_status()

        result = response.json()

        # 检查 API 返回状态
        if result.get('code') != 0:
            raise ValueError(f"API 错误: {result.get('code')} - {result.get('result', '未知错误')}")

        return result['data']

    except requests.RequestException as e:
        raise requests.RequestException(f"获取 access_token 失败: {e}")


def refresh_access_token(refresh_token: str, app_id: Optional[str] = None, app_key: Optional[str] = None) -> Dict:
    """
    刷新访问令牌

    Args:
        refresh_token: 刷新令牌
        app_id: 应用 APPID，默认使用全局 ACCESS_KEY
        app_key: 应用 APPKEY，默认使用全局 SECRET_KEY

    Returns:
        包含新的 access_token、refresh_token 等信息的字典

    Raises:
        requests.RequestException: 网络请求异常
        ValueError: API 返回错误
    """
    # 使用传入参数或默认值
    app_id = app_id or ACCESS_KEY
    app_key = app_key or SECRET_KEY

    # 构建请求
    url = f'https://developer.kdocs.cn/api/v1/oauth2/refresh_token?app_id={app_id}'
    headers = {'Content-Type': 'application/json'}
    data = {
        'app_key': app_key,
        'refresh_token': refresh_token
    }

    try:
        response = requests.post(url, json=data, headers=headers, timeout=30)
        response.raise_for_status()

        result = response.json()

        # 检查 API 返回状态
        if result.get('code') != 0:
            raise ValueError(f"API 错误: {result.get('code')} - {result.get('result', '未知错误')}")

        return result['data']

    except requests.RequestException as e:
        raise requests.RequestException(f"刷新 access_token 失败: {e}")


if __name__ == '__main__':
    def test():
        method = http.HTTPMethod.POST
        uri = 'https://openapi.wps.cn/v7/doclibs'
        content_type = 'application/json'
        kso_date = 'Mon, 02 Jan 2006 15:04:05 GMT'
        request_body = '{"key": "value"}'

        res = kso1_sign(method, uri, content_type, kso_date, request_body)
        print(res)


    test()