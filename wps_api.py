appid = 'AK20250509HUPZKK'
appkey = '35a763bb9c7f10759dceb399b903dc28'

import hashlib
import hmac
import http

ACCESS_KEY = 'AK123456'
SECRET_KEY = 'sk098765'


def _get_kso1_signature(method, uri, content_type, kso_date, request_body):
    sha256_hex = ''
    if request_body is not None and len(request_body) > 0:
        sha256_obj = hashlib.sha256()
        sha256_obj.update(request_body.encode())
        sha256_hex = sha256_obj.hexdigest()

    mac = hmac.new(bytes(SECRET_KEY, 'utf-8'),
                   bytes('KSO-1' + method + uri + content_type + kso_date + sha256_hex, 'utf-8'),
                   hashlib.sha256)
    return mac.hexdigest()


def kso1_sign(method, uri, content_type, kso_date, request_body):
    kso_signature = _get_kso1_signature(method, uri, content_type, kso_date, request_body)
    authorization = 'KSO-1 {}:{}'.format(ACCESS_KEY, kso_signature)
    return {
        'X-Kso-Date': kso_date,
        'X-Kso-Authorization': authorization
    }


if __name__ == '__main__':
    def test():
        method = http.HTTPMethod.POST
        uri = '/v7/test/body'
        content_type = 'application/json'
        kso_date = 'Mon, 02 Jan 2006 15:04:05 GMT'
        request_body = '{"key": "value"}'

        res = kso1_sign(method, uri, content_type, kso_date, request_body)
        print(res)


    test()